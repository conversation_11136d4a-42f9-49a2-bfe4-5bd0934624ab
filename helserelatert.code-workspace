{
    "folders": [
        {
            "name": "helserelatert",
            "path": "."
        }
    ],
    "settings": {

        // Editor Settings
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.detectIndentation": false,
        "files.eol": "\n",
        "files.insertFinalNewline": true,
        "files.trimTrailingWhitespace": true,

        // Terminal
        "terminal.integrated.defaultProfile.windows": "PowerShell",

        "augment.chat.userGuidelines": "This is a Windows system using PowerShell. Always use PowerShell syntax and commands, not cmd.",
        "terminal.integrated.suggest.enabled": true,
        "chat.mcp.discovery.enabled": true,
        "explorer.confirmDelete": false,

        // Add your Augment MCP configuration here:
        "augment.advanced": {
            "mcpServers": [
                {
                    "name": "supabase",
                    "command": "cmd",
                    "args": ["/c", "npx", "-y", "@supabase/mcp-server-supabase@latest", "--access-token", "********************************************"]
                },
                {
                    "name": "context7",
                    "command": "cmd",
                    "args": ["/c", "npx", "-y", "@upstash/context7-mcp"]
                },
                {
                    "name": "puppeteer",
                    "command": "cmd",
                    "args": ["/c", "npx", "-y", "@modelcontextprotocol/server-puppeteer"]
                },
                {
                    "name": "file-system-mcp",
                    "command": "cmd",
                    "args": ["/c", "npx", "-y", "@shtse8/filesystem-mcp"]
                },
            ]
        },
    },
}